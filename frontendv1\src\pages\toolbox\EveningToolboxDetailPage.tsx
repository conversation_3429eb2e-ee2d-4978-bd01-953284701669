import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from '@apollo/client';
import {
  ArrowLeft,
  AlertCircle,
  Calendar,
  User,
  FileText,
  Moon,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react';
import FloatingCard from '../../components/layout/FloatingCard';
import { GET_EVENING_TOOLBOX_BY_ID } from '../../graphql/queries';

interface NewHazard {
  description: string;
  controlMeasures: string[];
}

interface ExistingHazard {
  hazardId: number;
  controlMeasures: string[];
}

interface EveningToolboxJob {
  jobId: number;
  newHazards: NewHazard[];
  existingHazards: ExistingHazard[];
  isClosed: boolean;
}

interface EveningToolbox {
  id: number;
  jobs: EveningToolboxJob[];
  closedDate: string;
  conductorId: number;
  conductor: {
    id: number;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}



const EveningToolboxDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId, eveningToolboxId } = useParams<{ siteId: string; eveningToolboxId: string }>();

  const { data, loading, error } = useQuery(GET_EVENING_TOOLBOX_BY_ID, {
    variables: { id: parseInt(eveningToolboxId || '0') },
    skip: !eveningToolboxId
  });

  const eveningToolbox: EveningToolbox = data?.eveningToolboxById;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const breadcrumbs = [
    { name: 'Site Dashboard', path: `/sites/${siteId}/dashboard` },
    { name: 'Toolbox', path: `/sites/${siteId}/toolbox` },
    { name: 'Evening Toolbox', path: `/sites/${siteId}/toolbox/evening-toolbox` },
    { name: 'Details', path: `/sites/${siteId}/toolbox/evening-toolbox/${eveningToolboxId}` }
  ];

  if (loading) {
    return (
      <FloatingCard title="Evening Toolbox Details" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  if (error) {
    return (
      <FloatingCard title="Evening Toolbox Details" breadcrumbs={breadcrumbs}>
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <p className="text-red-600">
            {error ? `Error loading evening toolbox: ${error.message}` : 'Evening toolbox not found'}
          </p>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Evening Toolbox Details" breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {/* Header Info */}
        <div className="bg-indigo-50 p-6 rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Moon className="h-8 w-8 text-indigo-600" />
              <h2 className="text-2xl font-bold text-indigo-900">Evening Toolbox #{eveningToolbox.id}</h2>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-indigo-800">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span><strong>Closed Date:</strong> {formatDate(eveningToolbox.closedDate)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <span><strong>Created:</strong> {formatDateTime(eveningToolbox.createdAt)}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <span><strong>Updated:</strong> {formatDateTime(eveningToolbox.updatedAt)}</span>
            </div>
          </div>
        </div>

        {/* Conductor Information */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <User className="h-5 w-5 mr-2" />
            Conductor
          </h3>
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
              <User className="h-5 w-5 text-indigo-600" />
            </div>
            <div>
              <p className="font-medium text-gray-900">{eveningToolbox.conductor.name}</p>
              <p className="text-sm text-gray-500">Evening Toolbox Conductor</p>
            </div>
          </div>
        </div>

        {/* Jobs Overview */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <FileText className="h-5 w-5 mr-2 text-blue-600" />
            Jobs Summary
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{eveningToolbox.jobs.length}</div>
              <div className="text-sm text-blue-800">Total Jobs</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {eveningToolbox.jobs.filter(job => job.isClosed).length}
              </div>
              <div className="text-sm text-green-800">Closed Jobs</div>
            </div>
            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {eveningToolbox.jobs.filter(job => !job.isClosed).length}
              </div>
              <div className="text-sm text-orange-800">Open Jobs</div>
            </div>
          </div>
        </div>

        {/* Jobs Details */}
        {eveningToolbox.jobs.map((job) => (
          <div key={job.jobId} className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center justify-between">
              <div className="flex items-center">
                <FileText className="h-5 w-5 mr-2 text-blue-600" />
                Job #{job.jobId}
              </div>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                job.isClosed
                  ? 'bg-green-100 text-green-800'
                  : 'bg-orange-100 text-orange-800'
              }`}>
                {job.isClosed ? 'Closed' : 'Open'}
              </span>
            </h3>

            {/* New Hazards */}
            {job.newHazards.length > 0 && (
              <div className="mb-4">
                <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-2 text-red-500" />
                  New Hazards ({job.newHazards.length})
                </h4>
                <div className="space-y-3">
                  {job.newHazards.map((hazard, hazardIndex) => (
                    <div key={hazardIndex} className="bg-red-50 border border-red-200 rounded-lg p-4">
                      <p className="font-medium text-red-900 mb-2">{hazard.description}</p>
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-red-800">Control Measures:</p>
                        <ul className="list-disc list-inside text-sm text-red-700 space-y-1">
                          {hazard.controlMeasures.map((measure, measureIndex) => (
                            <li key={measureIndex}>{measure}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Existing Hazards */}
            {job.existingHazards.length > 0 && (
              <div className="mb-4">
                <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-2 text-orange-500" />
                  Existing Hazards ({job.existingHazards.length})
                </h4>
                <div className="space-y-3">
                  {job.existingHazards.map((hazard, hazardIndex) => (
                    <div key={hazardIndex} className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                      <p className="font-medium text-orange-900 mb-2">Hazard ID: {hazard.hazardId}</p>
                      <div className="space-y-1">
                        <p className="text-sm font-medium text-orange-800">Control Measures:</p>
                        <ul className="list-disc list-inside text-sm text-orange-700 space-y-1">
                          {hazard.controlMeasures.map((measure, measureIndex) => (
                            <li key={measureIndex}>{measure}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* No Hazards Message */}
            {job.newHazards.length === 0 && job.existingHazards.length === 0 && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <p className="text-green-800 font-medium">No hazards reported for this job</p>
                </div>
              </div>
            )}
          </div>
        ))}

        {/* Summary Statistics */}
        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <div className="flex items-center space-x-2">
                <FileText className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-gray-900">Total Jobs</span>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                {eveningToolbox.jobs.length} jobs reviewed
              </p>
            </div>

            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-orange-500" />
                <span className="font-medium text-gray-900">Hazards</span>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                {eveningToolbox.jobs.reduce((total, job) =>
                  total + job.newHazards.length + job.existingHazards.length, 0
                )} hazards identified
              </p>
            </div>

            <div className="bg-white p-4 rounded-lg border border-gray-200">
              <div className="flex items-center space-x-2">
                {eveningToolbox.jobs.filter(job => job.isClosed).length === eveningToolbox.jobs.length ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <XCircle className="h-5 w-5 text-orange-500" />
                )}
                <span className="font-medium text-gray-900">Status</span>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                {eveningToolbox.jobs.filter(job => job.isClosed).length === eveningToolbox.jobs.length
                  ? 'All jobs closed'
                  : `${eveningToolbox.jobs.filter(job => !job.isClosed).length} jobs still open`
                }
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between pt-6 border-t border-gray-200">
          <button
            onClick={() => navigate(`/sites/${siteId}/toolbox/evening-toolbox`)}
            className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Evening Toolbox</span>
          </button>

          <button
            onClick={() => navigate(`/sites/${siteId}/toolbox/evening-toolbox/fill`)}
            className="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
          >
            Create New Evening Toolbox
          </button>
        </div>
      </div>
    </FloatingCard>
  );
};

export default EveningToolboxDetailPage;
