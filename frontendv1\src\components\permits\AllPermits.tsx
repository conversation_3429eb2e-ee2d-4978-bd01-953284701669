import React, { useState, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@apollo/client';
import {
  FileText,
  Search,
  Filter,
  ChevronRight,
  Flame,
  HardHat,
  Zap,
  Mountain,
  Shield,
  Shovel
} from 'lucide-react';
import { GET_ALL_PERMITS } from '../../graphql/queries';
import UniversalFilter, { TagOption } from '../shared/UniversalFilter';

interface AllPermitsProps {
  siteId: string;
}

interface AllPermit {
  id: number;
  jobId: number;
  job: {
    id: number;
    title: string;
    description: string;
    location: string;
  };
  ptwRefNumber: string;
  projectName: string;
  startingDateTime: string;
  endingDateTime: string;
  status: string;
  permitType: string;
  isolation: string[];
  inspections: string[];
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy: string;
}

const AllPermits: React.FC<AllPermitsProps> = ({ siteId }) => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPermitType, setSelectedPermitType] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  // Fetch all permits
  const { data, loading, error } = useQuery<{ allPermits: AllPermit[] }>(GET_ALL_PERMITS);

  // Permit type filters
  const permitTypeFilters: TagOption[] = [
    { id: "GENERAL_WORK_PERMIT", name: "General Work" },
    { id: "HOT_WORK_PERMIT", name: "Hot Work" },
    { id: "CONFINED_SPACE_ENTRY_PERMIT", name: "Confined Space" },
    { id: "WORK_AT_HEIGHT_PERMIT", name: "Work at Height" },
    { id: "EXCAVATION_PERMIT", name: "Excavation" }
  ];

  // Status filters
  const statusFilters: TagOption[] = [
    { id: "DRAFTED", name: "Drafted" },
    { id: "OPENED", name: "Active" },
    { id: "CANCELLED", name: "Cancelled" },
    { id: "PENDING_CLOSURE", name: "Pending Closure" },
    { id: "CLOSED", name: "Closed" },
    { id: "VOIDED", name: "Voided" },
    { id: "EXPIRED", name: "Expired" }
  ];

  // Helper functions
  const getPermitTypeConfig = (permitType: string) => {
    switch (permitType) {
      case "GENERAL_WORK_PERMIT":
        return { icon: <FileText className="h-5 w-5" />, color: "text-gray-600", name: "General Work" };
      case "HOT_WORK_PERMIT":
        return { icon: <Flame className="h-5 w-5" />, color: "text-red-600", name: "Hot Work" };
      case "CONFINED_SPACE_ENTRY_PERMIT":
        return { icon: <Shield className="h-5 w-5" />, color: "text-blue-600", name: "Confined Space" };
      case "WORK_AT_HEIGHT_PERMIT":
        return { icon: <HardHat className="h-5 w-5" />, color: "text-orange-600", name: "Work at Height" };
      case "EXCAVATION_PERMIT":
        return { icon: <Shovel className="h-5 w-5" />, color: "text-yellow-600", name: "Excavation" };
      default:
        return { icon: <FileText className="h-5 w-5" />, color: "text-gray-600", name: "General Work" };
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: { [key: string]: { color: string; label: string } } = {
      "DRAFTED": { color: "bg-gray-100 text-gray-800", label: "Drafted" },
      "OPENED": { color: "bg-green-100 text-green-800", label: "Active" },
      "CANCELLED": { color: "bg-red-100 text-red-800", label: "Cancelled" },
      "PENDING_CLOSURE": { color: "bg-yellow-100 text-yellow-800", label: "Pending Closure" },
      "CLOSED": { color: "bg-blue-100 text-blue-800", label: "Closed" },
      "VOIDED": { color: "bg-purple-100 text-purple-800", label: "Voided" },
      "EXPIRED": { color: "bg-red-100 text-red-800", label: "Expired" }
    };

    const config = statusConfig[status] || { color: "bg-gray-100 text-gray-800", label: status };
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  // Filter permits
  const filteredPermits = useMemo(() => {
    if (!data?.allPermits) return [];

    return data.allPermits.filter(permit => {
      const matchesSearch = searchQuery === "" ||
        permit.job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        permit.ptwRefNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
        permit.job.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        permit.job.location.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesType = selectedPermitType === "all" || permit.permitType === selectedPermitType;
      const matchesStatus = selectedStatus === "all" || permit.status === selectedStatus;

      return matchesSearch && matchesType && matchesStatus;
    });
  }, [data, searchQuery, selectedPermitType, selectedStatus]);

  // Handle permit click
  const handlePermitClick = (permit: AllPermit) => {
    const permitTypeRoutes: { [key: string]: string } = {
      "GENERAL_WORK_PERMIT": "general-work",
      "HOT_WORK_PERMIT": "hot-work",
      "CONFINED_SPACE_ENTRY_PERMIT": "confined-space",
      "WORK_AT_HEIGHT_PERMIT": "work-at-height",
      "EXCAVATION_PERMIT": "excavation"
    };

    const route = permitTypeRoutes[permit.permitType] || "general-work";
    navigate(`/sites/${siteId}/permits/${route}/${permit.id}`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading permits...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-red-500">Error loading permits: {error.message}</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">All Permits</h2>
          <p className="text-sm text-gray-600">Complete list of all permits. Click on a permit to view details.</p>
        </div>
        <div className="text-sm text-gray-600">
          Total {filteredPermits.length} permits
        </div>
      </div>

      {/* Search and Filters */}
      <div className="space-y-4">
        <UniversalFilter
          variant="search-tags"
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          searchPlaceholder="Search by permit number, job title, description, or location..."
          tags={[{ id: "all", name: "All Types" }, ...permitTypeFilters]}
          selectedTagId={selectedPermitType}
          onTagChange={setSelectedPermitType}
        />

        <div className="flex items-center space-x-4">
          <label className="text-sm font-medium text-gray-700">Status:</label>
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Statuses</option>
            {statusFilters.map(status => (
              <option key={status.id} value={status.id}>{status.name}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Permits Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredPermits.map((permit) => {
          const config = getPermitTypeConfig(permit.permitType);
          return (
            <div
              key={permit.id}
              onClick={() => handlePermitClick(permit)}
              className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <div className={config.color}>
                    {config.icon}
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 text-sm">{permit.ptwRefNumber}</h3>
                    <p className="text-xs text-gray-500">{config.name}</p>
                  </div>
                </div>
                {getStatusBadge(permit.status)}
              </div>

              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-900">{permit.job.title}</h4>
                <p className="text-sm text-gray-600 line-clamp-2">{permit.job.description}</p>

                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>{permit.job.location}</span>
                  <span>{new Date(permit.createdAt).toLocaleDateString()}</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">Project: {permit.projectName}</span>
                  <span className="text-xs text-gray-500">By {permit.createdBy}</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Empty State */}
      {filteredPermits.length === 0 && (
        <div className="text-center py-12">
          <FileText className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No permits found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchQuery || selectedPermitType !== "all" || selectedStatus !== "all"
              ? "No permits match your current filters."
              : "No permits have been created yet."
            }
          </p>
        </div>
      )}
    </div>
  );
};

export default AllPermits;
