import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from '@apollo/client';
import { 
  Plus, 
  Moon, 
  Calendar, 
  User, 
  ArrowRight,
  AlertCircle,
  FileText
} from 'lucide-react';
import FloatingCard from '../../components/layout/FloatingCard';
import { GET_ALL_EVENING_TOOLBOXES } from '../../graphql/queries';

interface EveningToolbox {
  id: number;
  date: string;
  conductor?: {
    id: number;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}

const EveningToolboxDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();

  const { data, loading, error } = useQuery(GET_ALL_EVENING_TOOLBOXES);
  const eveningToolboxes: EveningToolbox[] = data?.allEveningToolboxes || [];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const breadcrumbs = [
    { name: 'Site Dashboard', path: `/sites/${siteId}/dashboard` },
    { name: 'Toolbox', path: `/sites/${siteId}/toolbox` },
    { name: 'Evening Toolbox', path: `/sites/${siteId}/toolbox/evening-toolbox` }
  ];

  if (loading) {
    return (
      <FloatingCard title="Evening Toolbox Dashboard" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  if (error) {
    return (
      <FloatingCard title="Evening Toolbox Dashboard" breadcrumbs={breadcrumbs}>
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <p className="text-red-600">Error loading evening toolboxes: {error.message}</p>
        </div>
      </FloatingCard>
    );
  }
// console.log(JSON.stringify(eveningToolboxes?.sort((a,b)=> new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())))
  return (
    <FloatingCard title="Evening Toolbox Dashboard" breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {/* Header with Create Button */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Moon className="h-8 w-8 text-indigo-600" />
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Evening Toolbox</h2>
              <p className="text-gray-600">Manage evening toolbox meetings and records</p>
            </div>
          </div>
          
          <button
            onClick={() => navigate(`/sites/${siteId}/toolbox/evening-toolbox/fill`)}
            className="flex items-center space-x-2 bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors"
          >
            <Plus className="h-5 w-5" />
            <span>Create Evening Toolbox</span>
          </button>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-indigo-50 p-6 rounded-lg">
            <div className="flex items-center">
              <Moon className="h-8 w-8 text-indigo-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-indigo-600">Total Evening Toolboxes</p>
                <p className="text-2xl font-bold text-indigo-900">{eveningToolboxes.length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-blue-50 p-6 rounded-lg">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-blue-600">This Month</p>
                <p className="text-2xl font-bold text-blue-900">
                  {eveningToolboxes.filter(et => {
                    const etDate = new Date(et.date);
                    const now = new Date();
                    return etDate.getMonth() === now.getMonth() && etDate.getFullYear() === now.getFullYear();
                  }).length}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-green-50 p-6 rounded-lg">
            <div className="flex items-center">
              <User className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-green-600">This Week</p>
                <p className="text-2xl font-bold text-green-900">
                  {eveningToolboxes.filter(et => {
                    const etDate = new Date(et.date);
                    const now = new Date();
                    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    return etDate >= weekAgo && etDate <= now;
                  }).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Evening Toolboxes List */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Recent Evening Toolboxes</h3>
          
          {eveningToolboxes.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <Moon className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600 text-lg">No evening toolboxes created yet</p>
              <p className="text-gray-500 text-sm mt-2">Create your first evening toolbox to get started</p>
              <button
                onClick={() => navigate(`/sites/${siteId}/toolbox/evening-toolbox/fill`)}
                className="mt-4 flex items-center space-x-2 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 mx-auto"
              >
                <Plus className="h-4 w-4" />
                <span>Create Evening Toolbox</span>
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {eveningToolboxes && eveningToolboxes
                .map((eveningToolbox) => (
                  <div
                    key={eveningToolbox.id}
                    className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow bg-white"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-start space-x-4">
                        <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                          <Moon className="h-6 w-6 text-indigo-600" />
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h4 className="text-lg font-semibold text-gray-900">
                              Evening Toolbox #{eveningToolbox.id}
                            </h4>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4" />
                              <span>Date: {formatDate(eveningToolbox.date)}</span>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                              <User className="h-4 w-4" />
                              <span>Conductor: {eveningToolbox.conductor?.name || 'Not assigned'}</span>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                              <FileText className="h-4 w-4" />
                              <span>Created: {formatDateTime(eveningToolbox.createdAt)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <button
                        onClick={() => navigate(`/sites/${siteId}/toolbox/evening-toolbox/${eveningToolbox.id}`)}
                        className="flex items-center space-x-2 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition-colors"
                      >
                        <span>View Details</span>
                        <ArrowRight className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))}
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={() => navigate(`/sites/${siteId}/toolbox/evening-toolbox/fill`)}
              className="flex items-center space-x-3 p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Plus className="h-6 w-6 text-indigo-600" />
              <div className="text-left">
                <p className="font-medium text-gray-900">Create New Evening Toolbox</p>
                <p className="text-sm text-gray-500">Start a new evening toolbox meeting</p>
              </div>
            </button>
            
            <button
              onClick={() => navigate(`/sites/${siteId}/toolbox`)}
              className="flex items-center space-x-3 p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <ArrowRight className="h-6 w-6 text-blue-600" />
              <div className="text-left">
                <p className="font-medium text-gray-900">Back to Main Toolbox</p>
                <p className="text-sm text-gray-500">Return to regular toolbox dashboard</p>
              </div>
            </button>
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default EveningToolboxDashboardPage;
