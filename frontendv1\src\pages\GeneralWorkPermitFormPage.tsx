import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useQuery, useMutation } from '@apollo/client';
import { ArrowLeft, ArrowRight, Save, FileText, CheckCircle, Plus, X, Camera } from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import { GET_JOB_BY_ID, GET_ALL_WORKERS } from '../graphql/queries';
import { CREATE_GENERAL_WORK_PERMIT } from '../graphql/mutations';
import { toast } from 'react-toastify';

interface Job {
  id: number;
  title: string;
  description: string;
  location: string;
  status: string;
  requiredPermits: string[];
  ppEs: string[];
  precautionsRequired: string[];
  startDate: string;
  dueDate: string;
  requestedBy: {
    id: number;
    name: string;
  };
  hazards: Array<{
    id: number;
    description: string;
    controlMeasures: Array<{
      id: number;
      description: string;
      closed: boolean;
    }>;
  }>;
  documents: Array<{
    id: number;
    name: string;
    url: string;
  }>;
  createdAt: string;
}

interface Worker {
  id: number;
  name: string;
  company: string;
  trades: Array<{
    id: number;
    name: string;
  }>;
}

interface FormData {
  ptwRefNumber: string;
  projectName: string;
  startingDateTime: string;
  isolation: string[];
  inspections: string[];
  permitIssuer: {
    competentPersonIds: number[];
    authorisedPersonsIds: number[];
  };
  signOff: {
    workerIds: number[];
  };
  attendancePictureFile?: File;
}

const GeneralWorkPermitFormPage: React.FC = () => {
  const { siteId, jobId } = useParams<{ siteId: string; jobId: string }>();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newIsolationItem, setNewIsolationItem] = useState('');
  const [newInspectionItem, setNewInspectionItem] = useState('');

  // Form data state
  const [formData, setFormData] = useState<FormData>({
    ptwRefNumber: '',
    projectName: '',
    startingDateTime: new Date().toISOString().slice(0, 16),
    isolation: [],
    inspections: [],
    permitIssuer: {
      competentPersonIds: [],
      authorisedPersonsIds: []
    },
    signOff: {
      workerIds: []
    }
  });

  // Fetch job data
  const { data: jobData, loading: jobLoading, error: jobError } = useQuery<{ jobById: Job[] }>(
    GET_JOB_BY_ID,
    {
      variables: { id: parseInt(jobId || '0') },
      skip: !jobId
    }
  );

  // Fetch workers for permit issuer and sign off
  const { data: workersData, loading: workersLoading } = useQuery<{ allWorkers: Worker[] }>(
    GET_ALL_WORKERS
  );

  // Create permit mutation
  const [createGeneralWorkPermit] = useMutation(CREATE_GENERAL_WORK_PERMIT, {
    onCompleted: (data) => {
      toast.success('General Work Permit created successfully!');
      navigate(`/sites/${siteId}/permits`);
    },
    onError: (error) => {
      console.error('Error creating permit:', error);
      toast.error('Failed to create permit. Please try again.');
    }
  });

  const job = jobData?.jobById?.[0];
  const workers = workersData?.allWorkers || [];

  useEffect(() => {
    if (job) {
      setFormData(prev => ({
        ...prev,
        projectName: job.title
      }));
    }
  }, [job]);

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNestedInputChange = (parentField: keyof FormData, childField: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parentField]: {
        ...(prev[parentField] as any),
        [childField]: value
      }
    }));
  };

  const addIsolationItem = () => {
    if (newIsolationItem.trim()) {
      setFormData(prev => ({
        ...prev,
        isolation: [...prev.isolation, newIsolationItem.trim()]
      }));
      setNewIsolationItem('');
    }
  };

  const removeIsolationItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      isolation: prev.isolation.filter((_, i) => i !== index)
    }));
  };

  const addInspectionItem = () => {
    if (newInspectionItem.trim()) {
      setFormData(prev => ({
        ...prev,
        inspections: [...prev.inspections, newInspectionItem.trim()]
      }));
      setNewInspectionItem('');
    }
  };

  const removeInspectionItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      inspections: prev.inspections.filter((_, i) => i !== index)
    }));
  };

  const handleWorkerSelection = (workerId: number, field: 'competentPersonIds' | 'authorisedPersonsIds' | 'workerIds') => {
    if (field === 'workerIds') {
      const currentIds = formData.signOff.workerIds;
      const newIds = currentIds.includes(workerId)
        ? currentIds.filter(id => id !== workerId)
        : [...currentIds, workerId];
      
      handleNestedInputChange('signOff', 'workerIds', newIds);
    } else {
      const currentIds = formData.permitIssuer[field];
      const newIds = currentIds.includes(workerId)
        ? currentIds.filter(id => id !== workerId)
        : [...currentIds, workerId];
      
      handleNestedInputChange('permitIssuer', field, newIds);
    }
  };

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    if (!jobId) {
      toast.error('Job ID is required');
      return;
    }

    setIsSubmitting(true);
    try {
      const permitInput = {
        jobId: parseInt(jobId),
        ptwRefNumber: formData.ptwRefNumber,
        projectName: formData.projectName,
        isolation: formData.isolation,
        inspections: formData.inspections,
        permitIssuer: formData.permitIssuer,
        signOff: formData.signOff,
        ...(formData.attendancePictureFile && { attendancePictureFile: formData.attendancePictureFile })
      };

      await createGeneralWorkPermit({
        variables: { input: permitInput }
      });
    } catch (error) {
      console.error('Error submitting permit:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setFormData(prev => ({
        ...prev,
        attendancePictureFile: file
      }));
    }
  };

  if (jobLoading || workersLoading) {
    return (
      <FloatingCard title="Loading...">
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading permit form...</div>
        </div>
      </FloatingCard>
    );
  }

  if (jobError || !job) {
    return (
      <FloatingCard title="Error">
        <div className="flex items-center justify-center h-64">
          <div className="text-red-500">Failed to load job details</div>
        </div>
      </FloatingCard>
    );
  }

  const getStepTitle = () => {
    switch (currentStep) {
      case 1: return 'General Work Permit Details';
      case 2: return 'Permit Issuer & Authorization';
      case 3: return 'Worker Sign-off & Attendance';
      default: return 'General Work Permit';
    }
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-6">
      {[1, 2, 3].map((step) => (
        <div key={step} className="flex items-center">
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
              step === currentStep
                ? 'bg-green-600 text-white'
                : step < currentStep
                ? 'bg-green-100 text-green-600'
                : 'bg-gray-200 text-gray-500'
            }`}
          >
            {step < currentStep ? <CheckCircle className="h-4 w-4" /> : step}
          </div>
          {step < 3 && (
            <div
              className={`w-16 h-1 mx-2 ${
                step < currentStep ? 'bg-green-600' : 'bg-gray-200'
              }`}
            />
          )}
        </div>
      ))}
    </div>
  );

  const renderStep1 = () => (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <h2 className="text-lg font-bold text-green-800 text-center mb-2">
          GENERAL PERMIT TO WORK
        </h2>
        <div className="text-xs text-red-600 font-medium text-center">
          This permit is valid for 7 days.
        </div>
      </div>

      {/* Basic Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            PTW Ref Number *
          </label>
          <input
            type="text"
            value={formData.ptwRefNumber}
            onChange={(e) => handleInputChange('ptwRefNumber', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Project Name *
          </label>
          <input
            type="text"
            value={formData.projectName}
            onChange={(e) => handleInputChange('projectName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
            required
          />
        </div>
      </div>

      {/* Starting From (Read-only, set to now) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Starting From
        </label>
        <input
          type="datetime-local"
          value={formData.startingDateTime}
          className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 cursor-not-allowed"
          readOnly
          disabled
        />
        <p className="text-xs text-gray-500 mt-1">Automatically set to current date and time</p>
      </div>

      {/* Other Permits in Use (from job required permits) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Other Permits in Use
        </label>
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            {job?.requiredPermits?.map((permit, index) => (
              <div key={index} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={true}
                  disabled
                  className="h-4 w-4 text-green-600 border-gray-300 rounded cursor-not-allowed"
                />
                <label className="text-sm text-gray-600">{permit}</label>
              </div>
            ))}
          </div>
          <p className="text-xs text-gray-500 mt-2">Based on job requirements (not editable)</p>
        </div>
      </div>

      {/* Description of Work (from job) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Description of Work
        </label>
        <textarea
          rows={3}
          value={job?.description || ''}
          className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 cursor-not-allowed"
          readOnly
          disabled
        />
        <p className="text-xs text-gray-500 mt-1">From job description (not editable)</p>
      </div>

      {/* Location (from job) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Location
        </label>
        <input
          type="text"
          value={job?.location || ''}
          className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 cursor-not-allowed"
          readOnly
          disabled
        />
        <p className="text-xs text-gray-500 mt-1">From job location (not editable)</p>
      </div>

      {/* Hazards (from job) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Hazards
        </label>
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="space-y-2">
            {job?.hazards?.map((hazard, index) => (
              <div key={hazard.id} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={true}
                  disabled
                  className="h-4 w-4 text-green-600 border-gray-300 rounded cursor-not-allowed"
                />
                <label className="text-sm text-gray-700">{hazard.description}</label>
              </div>
            ))}
          </div>
          <p className="text-xs text-gray-500 mt-2">From job hazards (not editable)</p>
        </div>
      </div>

      {/* Documents (from job) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Documents to be attached/availed at the work area
        </label>
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {job?.documents?.map((document, index) => (
              <div key={document.id} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={true}
                  disabled
                  className="h-4 w-4 text-green-600 border-gray-300 rounded cursor-not-allowed"
                />
                <label className="text-sm text-gray-700">{document.name}</label>
              </div>
            ))}
          </div>
          <p className="text-xs text-gray-500 mt-2">From job documents (not editable)</p>
        </div>
      </div>

      {/* Precautions (from job hazards control measures) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Precautions Required
        </label>
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="space-y-2">
            {job?.hazards?.flatMap(hazard =>
              hazard.controlMeasures?.map((measure, index) => (
                <div key={`${hazard.id}-${measure.id}`} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={true}
                    disabled
                    className="h-4 w-4 text-green-600 border-gray-300 rounded cursor-not-allowed"
                  />
                  <label className="text-sm text-gray-700">{measure.description}</label>
                </div>
              ))
            )}
          </div>
          <p className="text-xs text-gray-500 mt-2">From job control measures (not editable)</p>
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      {/* Isolation */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Isolation
        </label>
        <div className="space-y-3">
          {/* Existing isolation items */}
          <div className="space-y-2">
            {formData.isolation.map((item, index) => (
              <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-md">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={true}
                    readOnly
                    className="h-4 w-4 text-green-600 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">{item}</span>
                </div>
                <button
                  type="button"
                  onClick={() => removeIsolationItem(index)}
                  className="text-red-600 hover:text-red-800"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>

          {/* Add new isolation item */}
          <div className="flex space-x-2">
            <input
              type="text"
              value={newIsolationItem}
              onChange={(e) => setNewIsolationItem(e.target.value)}
              placeholder="Add isolation requirement..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
              onKeyPress={(e) => e.key === 'Enter' && addIsolationItem()}
            />
            <button
              type="button"
              onClick={addIsolationItem}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <Plus className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Inspections */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Inspections
        </label>
        <div className="space-y-3">
          {/* Existing inspection items */}
          <div className="space-y-2">
            {formData.inspections.map((item, index) => (
              <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-md">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={true}
                    readOnly
                    className="h-4 w-4 text-green-600 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">{item}</span>
                </div>
                <button
                  type="button"
                  onClick={() => removeInspectionItem(index)}
                  className="text-red-600 hover:text-red-800"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>

          {/* Add new inspection item */}
          <div className="flex space-x-2">
            <input
              type="text"
              value={newInspectionItem}
              onChange={(e) => setNewInspectionItem(e.target.value)}
              placeholder="Add inspection requirement..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
              onKeyPress={(e) => e.key === 'Enter' && addInspectionItem()}
            />
            <button
              type="button"
              onClick={addInspectionItem}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <Plus className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Permit Issuer */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Competent Persons */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Competent Persons (Permit Received)
          </label>
          <div className="border border-gray-300 rounded-md p-4 max-h-64 overflow-y-auto">
            {workers.map((worker) => (
              <div key={worker.id} className="flex items-center space-x-3 py-2">
                <input
                  type="checkbox"
                  checked={formData.permitIssuer.competentPersonIds.includes(worker.id)}
                  onChange={() => handleWorkerSelection(worker.id, 'competentPersonIds')}
                  className="h-4 w-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
                />
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">{worker.name}</div>
                  <div className="text-xs text-gray-500">{worker.company}</div>
                  <div className="text-xs text-gray-400">
                    {worker.trades?.map(trade => trade.name).join(', ')}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Authorized Persons */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Authorized Persons (Permit Issuer)
          </label>
          <div className="border border-gray-300 rounded-md p-4 max-h-64 overflow-y-auto">
            {workers.map((worker) => (
              <div key={worker.id} className="flex items-center space-x-3 py-2">
                <input
                  type="checkbox"
                  checked={formData.permitIssuer.authorisedPersonsIds.includes(worker.id)}
                  onChange={() => handleWorkerSelection(worker.id, 'authorisedPersonsIds')}
                  className="h-4 w-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
                />
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">{worker.name}</div>
                  <div className="text-xs text-gray-500">{worker.company}</div>
                  <div className="text-xs text-gray-400">
                    {worker.trades?.map(trade => trade.name).join(', ')}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Permit Return (Mirrored from Permit Issue) */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-3">Permit Return</h3>
        <p className="text-sm text-gray-600 mb-4">
          The permit return section will mirror the permit issuer selections above and will be automatically filled when the permit is returned.
        </p>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Selected Competent Persons
            </label>
            <div className="space-y-1">
              {formData.permitIssuer.competentPersonIds.map(id => {
                const worker = workers.find(w => w.id === id);
                return worker ? (
                  <div key={id} className="text-sm text-gray-600 bg-white p-2 rounded border">
                    {worker.name} - {worker.company}
                  </div>
                ) : null;
              })}
              {formData.permitIssuer.competentPersonIds.length === 0 && (
                <div className="text-sm text-gray-400 italic">No competent persons selected</div>
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Selected Authorized Persons
            </label>
            <div className="space-y-1">
              {formData.permitIssuer.authorisedPersonsIds.map(id => {
                const worker = workers.find(w => w.id === id);
                return worker ? (
                  <div key={id} className="text-sm text-gray-600 bg-white p-2 rounded border">
                    {worker.name} - {worker.company}
                  </div>
                ) : null;
              })}
              {formData.permitIssuer.authorisedPersonsIds.length === 0 && (
                <div className="text-sm text-gray-400 italic">No authorized persons selected</div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      {/* Sign Off Description */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-lg font-medium text-blue-900 mb-2">PTW Sign Off</h3>
        <p className="text-sm text-blue-800">
          By signing this permit to work, I accept to abide by the instituted control measures that will enhance safe working.
        </p>
      </div>

      {/* Worker Selection for Sign Off */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Select Workers for Sign Off
        </label>
        <div className="border border-gray-300 rounded-md p-4 max-h-96 overflow-y-auto">
          <div className="mb-4">
            <input
              type="text"
              placeholder="Search workers..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
            />
          </div>

          <div className="space-y-3">
            {workers.map((worker) => (
              <div key={worker.id} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-md hover:bg-gray-50">
                <input
                  type="checkbox"
                  checked={formData.signOff.workerIds.includes(worker.id)}
                  onChange={() => handleWorkerSelection(worker.id, 'workerIds')}
                  className="h-4 w-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
                />
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">{worker.name}</div>
                  <div className="text-xs text-gray-500">{worker.company}</div>
                  <div className="text-xs text-gray-400">
                    {worker.trades?.map(trade => trade.name).join(', ')}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Selected Workers Summary */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Selected Workers ({formData.signOff.workerIds.length})
        </label>
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          {formData.signOff.workerIds.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {formData.signOff.workerIds.map(id => {
                const worker = workers.find(w => w.id === id);
                return worker ? (
                  <div key={id} className="bg-white p-3 rounded border flex items-center justify-between">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{worker.name}</div>
                      <div className="text-xs text-gray-500">{worker.company}</div>
                    </div>
                    <button
                      type="button"
                      onClick={() => handleWorkerSelection(worker.id, 'workerIds')}
                      className="text-red-600 hover:text-red-800"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ) : null;
              })}
            </div>
          ) : (
            <div className="text-sm text-gray-400 italic text-center py-4">
              No workers selected for sign off
            </div>
          )}
        </div>
      </div>

      {/* Attendance Picture */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Attendance Picture
        </label>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
          <div className="text-center">
            <Camera className="mx-auto h-12 w-12 text-gray-400" />
            <div className="mt-4">
              <label htmlFor="attendance-picture" className="cursor-pointer">
                <span className="mt-2 block text-sm font-medium text-gray-900">
                  Take a picture of attendees
                </span>
                <span className="mt-1 block text-xs text-gray-500">
                  PNG, JPG, GIF up to 10MB
                </span>
              </label>
              <input
                id="attendance-picture"
                name="attendance-picture"
                type="file"
                accept="image/*"
                capture="environment"
                onChange={handleFileUpload}
                className="sr-only"
              />
            </div>
            <div className="mt-4">
              <button
                type="button"
                onClick={() => document.getElementById('attendance-picture')?.click()}
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                <Camera className="h-4 w-4 mr-2" />
                Take Picture
              </button>
            </div>
          </div>

          {formData.attendancePictureFile && (
            <div className="mt-4 text-center">
              <div className="text-sm text-green-600 font-medium">
                Picture selected: {formData.attendancePictureFile.name}
              </div>
              <div className="text-xs text-gray-500">
                Size: {(formData.attendancePictureFile.size / 1024 / 1024).toFixed(2)} MB
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Form Summary */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-3">Permit Summary</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-700">PTW Ref Number:</span>
            <span className="ml-2 text-gray-600">{formData.ptwRefNumber || 'Not set'}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Project Name:</span>
            <span className="ml-2 text-gray-600">{formData.projectName || 'Not set'}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Isolation Items:</span>
            <span className="ml-2 text-gray-600">{formData.isolation.length}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Inspection Items:</span>
            <span className="ml-2 text-gray-600">{formData.inspections.length}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Competent Persons:</span>
            <span className="ml-2 text-gray-600">{formData.permitIssuer.competentPersonIds.length}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Authorized Persons:</span>
            <span className="ml-2 text-gray-600">{formData.permitIssuer.authorisedPersonsIds.length}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Workers for Sign Off:</span>
            <span className="ml-2 text-gray-600">{formData.signOff.workerIds.length}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Attendance Picture:</span>
            <span className="ml-2 text-gray-600">{formData.attendancePictureFile ? 'Uploaded' : 'Not uploaded'}</span>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <FloatingCard title="General Work Permit Form">
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate(`/sites/${siteId}/permits`)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm leading-4 font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">General Work Permit</h1>
                <p className="text-sm text-gray-500">Step {currentStep} of 3 - {getStepTitle()}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-gray-400" />
              <span className="text-sm text-gray-500">Job: {job.title}</span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="px-2 py-4">
          <div className="max-w-6xl mx-auto">
            {/* Step Indicator */}
            {renderStepIndicator()}

            {/* Form Content */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              {currentStep === 1 && renderStep1()}
              {currentStep === 2 && renderStep2()}
              {currentStep === 3 && renderStep3()}
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-6">
              <button
                onClick={handlePrevious}
                disabled={currentStep === 1}
                className={`inline-flex items-center px-6 py-3 border border-gray-300 rounded-md text-sm font-medium ${
                  currentStep === 1
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </button>

              {currentStep < 3 ? (
                <button
                  onClick={handleNext}
                  className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  Next
                  <ArrowRight className="h-4 w-4 ml-2" />
                </button>
              ) : (
                <button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Submit Permit
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default GeneralWorkPermitFormPage;
